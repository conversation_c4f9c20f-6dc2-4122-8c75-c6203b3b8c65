ninja: Entering directory `C:\Users\<USER>\Desktop\PE\MMA301PE_250801_SE172625\android\app\.cxx\Debug\334c1u2k\x86_64'
[0/2] Re-checking globbed directories...
[1/50] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o
[2/50] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o
[3/50] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o
[4/50] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o
[5/50] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o
[6/50] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o
[7/50] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o
[8/50] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o
[9/50] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[10/50] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o
[11/50] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o
[12/50] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4196ba2bd055110446884898e1bed3e1/renderer/components/safeareacontext/States.cpp.o
[13/50] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o
[14/50] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o
[15/50] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o
[16/50] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o
[17/50] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/740777cfd539b515bd70cb45c936e5eb/safeareacontext/RNCSafeAreaViewState.cpp.o
[18/50] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/e07d90d9ccc4cf3fa129b23bb3ae70d4/components/safeareacontext/ShadowNodes.cpp.o
[19/50] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/740777cfd539b515bd70cb45c936e5eb/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o
[20/50] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/e07d90d9ccc4cf3fa129b23bb3ae70d4/components/safeareacontext/EventEmitters.cpp.o
[21/50] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/e69f29cdec8ff05a30d32843c87e4ad7/safeareacontext/ComponentDescriptors.cpp.o
[22/50] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/4196ba2bd055110446884898e1bed3e1/renderer/components/safeareacontext/Props.cpp.o
[23/50] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/723b723609bcea9dd62b526d333418b8/codegen/jni/safeareacontext-generated.cpp.o
[24/50] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/95a8c5e36c1c9241444bf0579d6bab00/safeareacontextJSI-generated.cpp.o
[25/50] Linking CXX shared library C:\Users\<USER>\Desktop\PE\MMA301PE_250801_SE172625\android\app\build\intermediates\cxx\Debug\334c1u2k\obj\x86_64\libreact_codegen_safeareacontext.so
[26/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/10452aaf9d8b3a9e3fbbde087d129de1/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o
[27/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/10452aaf9d8b3a9e3fbbde087d129de1/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o
[28/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6307a9e5e4dc060f3bd0a18758f51138/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o
[29/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/803444edb96b228de6877aeb5b3912bd/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o
[30/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/803444edb96b228de6877aeb5b3912bd/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o
[31/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/7e1ea95509c46aaa64709de6251b999d/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o
[32/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/10452aaf9d8b3a9e3fbbde087d129de1/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o
[33/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/7eadfc31722bd5bfc6a097ab9a3c6588/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o
[34/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c33ee7f4a4095a752b17c1494b0b01db/codegen/jni/react/renderer/components/rnscreens/States.cpp.o
[35/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e17ea3b6e902d9abcbe111a1958fcf70/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o
[36/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/e17ea3b6e902d9abcbe111a1958fcf70/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o
[37/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o
[38/50] Building CXX object CMakeFiles/appmodules.dir/C_/Users/<USER>/Desktop/PE/MMA301PE_250801_SE172625/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[39/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c33ee7f4a4095a752b17c1494b0b01db/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o
[40/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/38710efdf11bbdd4e1e7760a60e0e79b/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o
[41/50] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o
[42/50] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o
[43/50] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o
[44/50] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/38710efdf11bbdd4e1e7760a60e0e79b/renderer/components/rnscreens/ComponentDescriptors.cpp.o
[45/50] Linking CXX shared library C:\Users\<USER>\Desktop\PE\MMA301PE_250801_SE172625\android\app\build\intermediates\cxx\Debug\334c1u2k\obj\x86_64\libreact_codegen_rnscreens.so
[46/50] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o
[47/50] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o
[48/50] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o
[49/50] Building CXX object RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o
[50/50] Linking CXX shared library C:\Users\<USER>\Desktop\PE\MMA301PE_250801_SE172625\android\app\build\intermediates\cxx\Debug\334c1u2k\obj\x86_64\libappmodules.so
