{"version": 3, "sources": ["IndiscreteGestureHandler.ts"], "names": ["Gesture<PERSON>andler", "IndiscreteGestureHandler", "shouldEnableGestureOnSetup", "updateGestureConfig", "minPointers", "maxPointers", "props", "isGestureEnabledForEvent", "_recognizer", "pointer<PERSON><PERSON><PERSON>", "failed", "validPointerCount", "success"], "mappings": "AAAA,OAAOA,cAAP,MAA2B,kBAA3B;AAEA;AACA;AACA;;AACA,MAAeC,wBAAf,SAAgDD,cAAhD,CAA+D;AAC/B,MAA1BE,0BAA0B,GAAG;AAC/B,WAAO,KAAP;AACD;;AAEDC,EAAAA,mBAAmB,CAAC;AAAEC,IAAAA,WAAW,GAAG,CAAhB;AAAmBC,IAAAA,WAAW,GAAG,CAAjC;AAAoC,OAAGC;AAAvC,GAAD,EAAiD;AAClE,WAAO,MAAMH,mBAAN,CAA0B;AAC/BC,MAAAA,WAD+B;AAE/BC,MAAAA,WAF+B;AAG/B,SAAGC;AAH4B,KAA1B,CAAP;AAKD;;AAEDC,EAAAA,wBAAwB,CACtB;AAAEH,IAAAA,WAAF;AAAeC,IAAAA;AAAf,GADsB,EAEtBG,WAFsB,EAGtB;AAAEH,IAAAA,WAAW,EAAEI;AAAf,GAHsB,EAItB;AACA,QAAIA,aAAa,GAAGJ,WAApB,EAAiC;AAC/B,aAAO;AAAEK,QAAAA,MAAM,EAAE;AAAV,OAAP;AACD;;AACD,UAAMC,iBAAiB,GAAGF,aAAa,IAAIL,WAA3C;AACA,WAAO;AACLQ,MAAAA,OAAO,EAAED;AADJ,KAAP;AAGD;;AAzB4D;;AA2B/D,eAAeV,wBAAf", "sourcesContent": ["import GestureHandler from './GestureHandler';\n\n/**\n * The base class for **Rotation** and **Pinch** gesture handlers.\n */\nabstract class IndiscreteGestureHandler extends GestureHandler {\n  get shouldEnableGestureOnSetup() {\n    return false;\n  }\n\n  updateGestureConfig({ minPointers = 2, maxPointers = 2, ...props }) {\n    return super.updateGestureConfig({\n      minPointers,\n      maxPointers,\n      ...props,\n    });\n  }\n\n  isGestureEnabledForEvent(\n    { minPointers, maxPointers }: any,\n    _recognizer: any,\n    { maxPointers: pointerLength }: any\n  ) {\n    if (pointerLength > maxPointers) {\n      return { failed: true };\n    }\n    const validPointerCount = pointerLength >= minPointers;\n    return {\n      success: validPointerCount,\n    };\n  }\n}\nexport default IndiscreteGestureHandler;\n"]}