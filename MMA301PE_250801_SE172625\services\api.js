const API_URL = process.env.EXPO_PUBLIC_API_URL;

export const handbagService = {
  // Lấy tất cả handbags từ MockAPI
  getAllHandbags: async () => {
    try {
      const response = await fetch(`${API_URL}/SE172625`);
      if (!response.ok) {
        throw new Error('Failed to fetch handbags');
      }
      const data = await response.json();

      // Chuyển đổi dữ liệu để phù hợp với UI
      return data.map(item => ({
        ...item,
        url: item.uri, // Chuyển uri thành url
        percentOff: Math.round(item.percentOff * 100), // Chuyển decimal thành percentage
      }));
    } catch (error) {
      console.error('Error fetching handbags:', error);
      throw error;
    }
  },

  // Lấy handbag theo ID
  getHandbagById: async (id) => {
    try {
      const response = await fetch(`${API_URL}/SE172625/${id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch handbag');
      }
      const data = await response.json();

      // Chuyển đổi dữ liệu để phù hợp với UI
      return {
        ...data,
        url: data.uri, // Chuyển uri thành url
        percentOff: Math.round(data.percentOff * 100), // Chuyển decimal thành percentage
      };
    } catch (error) {
      console.error('Error fetching handbag:', error);
      throw error;
    }
  },
};
