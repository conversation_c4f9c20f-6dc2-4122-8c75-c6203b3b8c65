# SE172625 - Handbag Store App

React Native application for handbag management with Expo.

## Features

- Browse handbags with filtering by brand
- Add/remove favorites using AsyncStorage
- View detailed product information
- Bottom tab navigation (Home, Favorite, Customer)
- Gender-specific styling with icons

## Setup

1. Install dependencies:
```bash
npm install
```

2. Configure MockAPI endpoint in .env file:
```
EXPO_PUBLIC_API_URL=https://your-mockapi-url.mockapi.io/api/v1
```

3. Upload data.json to MockAPI.io with resource name "handbags"

4. Run the app:
```bash
npm start
npm run android
npm run ios
npm run web
```

## Project Structure

- `/screens` - App screens (Home, Detail, Favorite, Customer)
- `/services` - API and AsyncStorage services
- `/data.json` - Sample data for MockAPI

## Requirements Met

✅ React Native with Expo
✅ Bottom tab navigation (3 tabs)
✅ MockAPI integration
✅ AsyncStorage for favorites
✅ Brand filtering
✅ Gender display with icons
✅ Cost sorting (descending)
✅ Navigation to detail screen
✅ Environment variables (.env)
