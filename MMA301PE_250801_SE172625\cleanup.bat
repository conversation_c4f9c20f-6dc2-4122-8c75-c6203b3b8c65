@echo off
echo Cleaning up project for submission...

echo Removing node_modules...
rmdir /s /q node_modules 2>nul

echo Removing .expo folder...
rmdir /s /q .expo 2>nul

echo Removing android build folders...
rmdir /s /q android\app\build 2>nul
rmdir /s /q android\build 2>nul
rmdir /s /q android\.gradle 2>nul

echo Cleanup completed!
echo.
echo Project is now ready for submission.
echo To restore for development:
echo 1. Run: npm install
echo 2. Run: npx expo run:android
pause
