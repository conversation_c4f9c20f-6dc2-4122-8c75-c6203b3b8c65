{"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "Platform", "isSearchBarAvailableForCurrentPlatform", "includes", "OS", "executeNativeBackPress", "exitApp", "compatibilityFlags", "isNewBackTitleImplementation", "usesHeaderFlexboxImplementation"], "sourceRoot": "../../src", "sources": ["utils.ts"], "mappings": "AAAA,SAASA,WAAW,EAAEC,QAAQ,QAAQ,cAAc;AAEpD,OAAO,MAAMC,sCAAsC,GAAG,CACpD,KAAK,EACL,SAAS,CACV,CAACC,QAAQ,CAACF,QAAQ,CAACG,EAAE,CAAC;AAEvB,OAAO,SAASC,sBAAsBA,CAAA,EAAG;EACvC;EACAL,WAAW,CAACM,OAAO,CAAC,CAAC;EACrB,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,kBAAkB,GAAG;EAChC;AACF;AACA;AACA;AACA;AACA;EACEC,4BAA4B,EAAE,IAAI;EAElC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,+BAA+B,EAAE;AACnC,CAAC", "ignoreList": []}