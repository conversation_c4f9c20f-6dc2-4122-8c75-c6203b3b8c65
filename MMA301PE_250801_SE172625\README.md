# MMA301PE_250801_SE172625 - Handbag Store App

React Native application for handbag management with Expo - Android Studio compatible.

## Features

- Browse handbags with filtering by brand
- Add/remove favorites using AsyncStorage  
- View detailed product information
- Bottom tab navigation (Home, Favorite, Customer)
- Gender-specific styling with icons
- Cost sorting (descending order)

## Setup Instructions

1. Install dependencies:
```bash
npm install
```

2. Configure MockAPI endpoint in .env file:
```
EXPO_PUBLIC_API_URL=https://your-mockapi-url.mockapi.io/api/v1
```

3. Upload data.json to MockAPI.io with resource name "SE172625"

4. For Android Studio development:
```bash
npx expo run:android
```

5. For Expo development:
```bash
npm start
npm run android
```

## Project Structure

- `/screens` - App screens (Home, Detail, Favorite, Customer)
- `/services` - API and AsyncStorage services  
- `/android` - Native Android project (generated)
- `/data.json` - Sample data for MockAPI
- `/.env` - Environment variables

## Requirements Met

✅ React Native with Expo
✅ Visual Studio Code development
✅ NPM package management
✅ Android Studio compatible build
✅ Bottom tab navigation (3 tabs)
✅ MockAPI integration with environment variables
✅ AsyncStorage for favorites
✅ Brand filtering on Home screen
✅ Gender display with male/female icons
✅ Cost sorting (descending)
✅ Navigation to detail screen
✅ Handbag data: handbagName, url, percentOff (as percentage)

## Important Notes

- The endpoint MUST be added to environment variables in .env file
- Resource name on MockAPI.io should be "SE172625"
- No syntax or compilation errors
- Optimized for Android Studio development
- No web dependencies included
