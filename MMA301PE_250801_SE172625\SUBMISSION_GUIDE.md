# Hướng dẫn nộp bài MMA301PE_250801_SE172625

## Tr<PERSON><PERSON><PERSON> khi nộp bài

1. **Chạy cleanup script để giảm kích thước:**
   ```bash
   cleanup.bat
   ```

2. **<PERSON><PERSON><PERSON> tra các file bắt buộc có đầy đủ:**
   - ✅ App.js (Main navigation)
   - ✅ screens/ (HomeScreen, DetailScreen, FavoriteScreen, CustomerScreen)
   - ✅ services/ (api.js, favoriteService.js)
   - ✅ .env (MockAPI endpoint)
   - ✅ data.json (Sample data)
   - ✅ android/ (Native Android project)
   - ✅ package.json (Dependencies)
   - ✅ README.md (Documentation)

## C<PERSON>u hình MockAPI

1. Truy cập https://mockapi.io/
2. Tạo resource với tên: **SE172625**
3. Upload dữ liệu từ file `data.json`
4. Copy endpoint URL vào file `.env`

## Kiể<PERSON> tra yêu cầu đề bài

### ✅ Task 1: Create React Native application
- [x] Tên dự án: MMA301PE_250801_SE172625
- [x] 3 bottom tabs: Home, Favorite, Customer
- [x] Switchable navigation

### ✅ Task 2: Home screen implementation
- [x] Filtering handbags by brand
- [x] Data includes: handbagName, url, percentOff (as percentage)
- [x] Gender value with male/female icons
- [x] Descending order by cost
- [x] Navigation to Detail screen
- [x] AsyncStorage for favorites

### ✅ Technical Requirements
- [x] Visual Studio Code development
- [x] NPM and EXPO
- [x] Android Studio compatible
- [x] MockAPI.io integration
- [x] Environment variables in .env
- [x] No syntax/compilation errors
- [x] Resource name: SE172625

## Cách test dự án

1. **Khôi phục dependencies:**
   ```bash
   npm install
   ```

2. **Chạy trên Android:**
   ```bash
   npm run android
   ```

3. **Hoặc sử dụng Expo:**
   ```bash
   npm start
   ```

## Lưu ý quan trọng

- ⚠️ **KHÔNG** liên quan đến GitHub
- ⚠️ **KHÔNG** sử dụng web platform
- ⚠️ Endpoint **PHẢI** được thêm vào environment variables
- ⚠️ Resource name trên MockAPI **PHẢI** là "SE172625"
- ⚠️ Đảm bảo không có lỗi syntax trước khi nộp

## Cấu trúc nộp bài

```
MMA301PE_250801_SE172625/
├── App.js
├── screens/
├── services/
├── android/
├── .env
├── data.json
├── package.json
├── README.md
└── (other necessary files)
```

Dự án đã sẵn sàng nộp bài!
