{"root": "/Users/<USER>/code/expo/templates/expo-template-bare-minimum", "reactNativePath": "/Users/<USER>/code/expo/templates/expo-template-bare-minimum/node_modules/react-native", "dependencies": {"expo": {"root": "/Users/<USER>/code/expo/templates/expo-template-bare-minimum/node_modules/expo", "name": "expo", "platforms": {"android": {"sourceDir": "/Users/<USER>/code/expo/templates/expo-template-bare-minimum/node_modules/expo/android", "packageImportPath": "import expo.modules.ExpoModulesPackage;", "packageInstance": "new ExpoModulesPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "/Users/<USER>/code/expo/templates/expo-template-bare-minimum/node_modules/expo/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}}, "project": {"android": {"packageName": "com.helloworld", "sourceDir": "/Users/<USER>/code/expo/templates/expo-template-bare-minimum/android"}}}