ninja: Entering directory `C:\Users\<USER>\Desktop\PE\MMA301PE_250801_SE172625\node_modules\react-native-screens\android\.cxx\Debug\53646258\x86_64'
[1/6] Building CXX object CMakeFiles/rnscreens.dir/src/main/cpp/jni-adapter.cpp.o
[2/6] Building CXX object CMakeFiles/rnscreens.dir/7fb5e52e859e763116319a8276d597db/node_modules/react-native-screens/cpp/RNScreensTurboModule.cpp.o
[3/6] Building CXX object CMakeFiles/rnscreens.dir/7fb5e52e859e763116319a8276d597db/node_modules/react-native-screens/cpp/RNSScreenRemovalListener.cpp.o
[4/6] Building CXX object CMakeFiles/rnscreens.dir/src/main/cpp/OnLoad.cpp.o
[5/6] Building CXX object CMakeFiles/rnscreens.dir/src/main/cpp/NativeProxy.cpp.o
[6/6] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\53646258\obj\x86_64\librnscreens.so
