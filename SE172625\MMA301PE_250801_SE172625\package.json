{"name": "mma301pe_250801_se172625", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "expo": "~53.0.20", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "^2.27.1", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.13.1"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}