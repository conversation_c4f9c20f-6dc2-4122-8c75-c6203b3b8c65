@echo off
echo Cleaning up project for submission...

echo Removing node_modules...
rmdir /s /q node_modules 2>nul

echo Removing .expo folder...
rmdir /s /q .expo 2>nul

echo Removing dist folder...
rmdir /s /q dist 2>nul

echo Removing web-build folder...
rmdir /s /q web-build 2>nul

echo Cleanup completed!
echo.
echo To reinstall dependencies, run: npm install
echo To start the project, run: npm start
pause
