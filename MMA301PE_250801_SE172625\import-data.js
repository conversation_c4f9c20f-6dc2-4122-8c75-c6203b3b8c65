// Script để import dữ liệu v<PERSON><PERSON>
// Chạy: node import-data.js

const data = [
  {
    "id": "1",
    "handbagName": "Galuchat Serpenti Forever Crossbody in Red",
    "cost": 2798,
    "category": "Crossbody",
    "color": ["Red"],
    "gender": true,
    "uri": "https://cdn2.jomashop.com/media/catalog/product/cache/fc2ff48f80400416c47c36b80c0a3202/b/v/bvlgari-galuchat-serpenti-forever-crossbody-in-red-287977_4.jpg",
    "brand": "Bvlgari",
    "percentOff": 0.42
  },
  {
    "id": "2",
    "handbagName": "Ladies Monogram Print Shoulder Bag",
    "category": "Shoulder Bag",
    "cost": 215.98,
    "color": ["Vanilla", "Acorn", "Gray"],
    "gender": true,
    "uri": "https://cdn2.jomashop.com/media/catalog/product/cache/fc2ff48f80400416c47c36b80c0a3202/m/i/michael-kors-ladies-monogram-print-shoulder-bag-30h1ggrl8v149_6.jpg",
    "percentOff": 0.55,
    "brand": "Michael Kors"
  },
  {
    "id": "3",
    "handbagName": "Open Box - Logo Print Large Soft Belt Tote",
    "color": ["Silver", "Gray"],
    "cost": 583.19,
    "category": "Shoulder Bag",
    "gender": true,
    "uri": "https://cdn2.jomashop.com/media/catalog/product/cache/fc2ff48f80400416c47c36b80c0a3202/b/u/burberry-mini-shield-shoulder-bag-8082580_3.jpg",
    "brand": "Burberry",
    "percentOff": 0.56
  },
  {
    "id": "4",
    "handbagName": "Logo Embroided Shoulder Bag",
    "color": ["Red"],
    "cost": 595,
    "category": "Shoulder Bag",
    "gender": false,
    "uri": "https://cdn2.jomashop.com/media/catalog/product/cache/fc2ff48f80400416c47c36b80c0a3202/f/e/ferragamo-logo-embroided-shoulder-bag-241469-769602_3.jpg",
    "brand": "Ferragamo",
    "percentOff": 0.41
  },
  {
    "id": "5",
    "handbagName": "Cut Out-Detail Shoulder Bag",
    "color": ["Black", "Gray"],
    "cost": 588,
    "category": "Shoulder Bag",
    "gender": true,
    "uri": "https://cdn2.jomashop.com/media/catalog/product/cache/fc2ff48f80400416c47c36b80c0a3202/f/e/ferragamo-cut-outdetail-shoulder-bag-213942-762299_2.jpg",
    "brand": "Ferragamo",
    "percentOff": 0.4
  },
  {
    "id": "6",
    "handbagName": "Peekaboo Iseeu Mini Leather Bag",
    "color": ["Acciaio", "Palladio", "Gray"],
    "cost": 768,
    "category": "Shoulder Bag",
    "gender": false,
    "uri": "https://cdn2.jomashop.com/media/catalog/product/cache/fc2ff48f80400416c47c36b80c0a3202/f/e/fendi-peekaboo-iseeu-mini-leather-bag-7va530ark0f0fdy_2.jpg",
    "brand": "Fendi",
    "percentOff": 0.37
  },
  {
    "id": "7",
    "handbagName": "Olympia Leather Leather Crossbody Card Case",
    "color": ["Light Sesame"],
    "cost": 278,
    "category": "Card Case",
    "gender": true,
    "uri": "https://cdn2.jomashop.com/media/catalog/product/cache/fc2ff48f80400416c47c36b80c0a3202/b/u/burberry-olympia-leather-leather-crossbody-card-case-8040739_3.jpg",
    "brand": "Burberry",
    "percentOff": 0.47
  },
  {
    "id": "8",
    "handbagName": "Black Folded Business Card Holder",
    "color": ["Black"],
    "cost": 229,
    "category": "Card Holder",
    "gender": true,
    "uri": "https://cdn2.jomashop.com/media/catalog/product/cache/fc2ff48f80400416c47c36b80c0a3202/b/v/bvlgari-black-folded-business-card-holder-293552_2.jpg",
    "brand": "Bvlgari",
    "percentOff": 0.38
  },
  {
    "id": "9",
    "handbagName": "Small Empire Leather Wallet",
    "color": ["Beige"],
    "cost": 49.89,
    "category": "Card Case",
    "gender": true,
    "uri": "https://cdn2.jomashop.com/media/catalog/product/cache/fc2ff48f80400416c47c36b80c0a3202/m/i/michael-kors-small-empire-leather-wallet-32s3g8ed0l222_2.jpg",
    "brand": "Michael Kors",
    "percentOff": 0.62
  },
  {
    "id": "10",
    "handbagName": "Moments Sterling Silver Open Bangle, Size Small",
    "color": ["Brown", "Acorn"],
    "cost": 80.98,
    "category": "Wallets",
    "gender": true,
    "uri": "https://cdn2.jomashop.com/media/catalog/product/cache/fc2ff48f80400416c47c36b80c0a3202/m/i/michael-kors-ladies-signature-logo-carmen-medium-envelope-trifold-wallet-brownacorn-32s1gnme6b-252.jpg",
    "brand": "Michael Kors",
    "percentOff": 0.55
  },
  {
    "id": "11",
    "handbagName": "Peekaboo Leather Card Case Pouch",
    "color": ["Edamame"],
    "cost": 298.99,
    "category": "Card Case",
    "gender": true,
    "uri": "https://cdn2.jomashop.com/media/catalog/product/cache/fc2ff48f80400416c47c36b80c0a3202/f/e/fendi-peekaboo-leather-card-case-pouch-8ap161a91bf1l1c.jpg",
    "brand": "Fendi",
    "percentOff": 0.35
  },
  {
    "id": "12",
    "handbagName": "Logo Series Small Leather Tote",
    "color": ["White"],
    "cost": 1560,
    "category": "Tote Bags",
    "gender": true,
    "uri": "https://cdn2.jomashop.com/media/catalog/product/cache/fc2ff48f80400416c47c36b80c0a3202/b/v/bvlgari-logo-series-small-leather-tote-291959_3.jpg",
    "brand": "Bvlgari",
    "percentOff": 0.4
  },
  {
    "id": "13",
    "handbagName": "First Sight Flap Shoulder Bag",
    "color": ["Blue", "Light Blue"],
    "cost": 1748.98,
    "category": "Shoulder Bag",
    "gender": true,
    "uri": "https://cdn2.jomashop.com/media/catalog/product/cache/fc2ff48f80400416c47c36b80c0a3202/f/e/fendi-first-sight-flap-shoulder-bag-8bs072anx2f1l1k_3.jpg",
    "brand": "Fendi",
    "percentOff": 0.37
  },
  {
    "id": "14",
    "handbagName": "Serpenti Forever Compact Leather Wallet",
    "color": ["Light Blue", "Cyan"],
    "cost": 444.99,
    "category": "Wallets",
    "gender": true,
    "uri": "https://cdn2.jomashop.com/media/catalog/product/cache/fc2ff48f80400416c47c36b80c0a3202/a/m/Burberry-amethyst-tassel-bolo-bracelet-in-rose-plated-sterling-silver-jms003978.jpg",
    "brand": "Bvlgari",
    "percentOff": 0.4
  }
];

// Thay đổi URL này thành endpoint MockAPI của bạn
const MOCKAPI_URL = 'https://YOUR_MOCKAPI_ID.mockapi.io/api/v1/SE172625';

async function importData() {
  console.log('Bắt đầu import dữ liệu...');
  
  for (let i = 0; i < data.length; i++) {
    const item = data[i];
    try {
      const response = await fetch(MOCKAPI_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(item)
      });
      
      if (response.ok) {
        console.log(`✅ Đã import item ${i + 1}/${data.length}: ${item.handbagName}`);
      } else {
        console.log(`❌ Lỗi import item ${i + 1}: ${response.statusText}`);
      }
    } catch (error) {
      console.log(`❌ Lỗi import item ${i + 1}: ${error.message}`);
    }
    
    // Delay để tránh rate limit
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log('🎉 Hoàn thành import!');
}

// Chạy script
importData();
