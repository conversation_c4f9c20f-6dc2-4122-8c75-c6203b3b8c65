@echo off
"C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe" ^
  "-HC:\\Users\\<USER>\\Desktop\\PE\\MMA301PE_250801_SE172625\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni" ^
  "-DCMAKE_SYSTEM_NAME=Android" ^
  "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON" ^
  "-DCMAKE_SYSTEM_VERSION=24" ^
  "-DANDROID_PLATFORM=android-24" ^
  "-DANDROID_ABI=x86" ^
  "-DCMAKE_ANDROID_ARCH_ABI=x86" ^
  "-DANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006" ^
  "-DCMAKE_ANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006" ^
  "-DCMAKE_TOOLCHAIN_FILE=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\build\\cmake\\android.toolchain.cmake" ^
  "-DCMAKE_MAKE_PROGRAM=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe" ^
  "-DCMAKE_CXX_FLAGS=-O2 -frtti -fexceptions -Wall -Werror -std=c++20 -DANDROID" ^
  "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\Users\\<USER>\\Desktop\\PE\\MMA301PE_250801_SE172625\\node_modules\\react-native-gesture-handler\\android\\build\\intermediates\\cxx\\Debug\\6451zb3j\\obj\\x86" ^
  "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\Users\\<USER>\\Desktop\\PE\\MMA301PE_250801_SE172625\\node_modules\\react-native-gesture-handler\\android\\build\\intermediates\\cxx\\Debug\\6451zb3j\\obj\\x86" ^
  "-DCMAKE_BUILD_TYPE=Debug" ^
  "-DCMAKE_FIND_ROOT_PATH=C:\\Users\\<USER>\\Desktop\\PE\\MMA301PE_250801_SE172625\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\6451zb3j\\prefab\\x86\\prefab" ^
  "-BC:\\Users\\<USER>\\Desktop\\PE\\MMA301PE_250801_SE172625\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\6451zb3j\\x86" ^
  -GNinja ^
  "-DREACT_NATIVE_DIR=C:\\Users\\<USER>\\Desktop\\PE\\MMA301PE_250801_SE172625\\node_modules\\react-native" ^
  "-DREACT_NATIVE_MINOR_VERSION=79" ^
  "-DANDROID_STL=c++_shared" ^
  "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON"
