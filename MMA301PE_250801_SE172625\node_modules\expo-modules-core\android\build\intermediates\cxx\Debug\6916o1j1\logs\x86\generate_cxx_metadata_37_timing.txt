# C/C++ build system timings
generate_cxx_metadata
  [gap of 23ms]
  create-invalidation-state 10ms
  generate-prefab-packages
    [gap of 13ms]
    exec-prefab 613ms
    [gap of 14ms]
  generate-prefab-packages completed in 640ms
  execute-generate-process
    exec-configure 1810ms
    [gap of 123ms]
  execute-generate-process completed in 1935ms
  [gap of 99ms]
generate_cxx_metadata completed in 2714ms

