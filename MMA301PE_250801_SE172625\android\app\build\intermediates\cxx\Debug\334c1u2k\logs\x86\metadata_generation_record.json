[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: x86", "file_": "C:\\Users\\<USER>\\Desktop\\PE\\MMA301PE_250801_SE172625\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON C:\\Users\\<USER>\\Desktop\\PE\\MMA301PE_250801_SE172625\\android\\app\\.cxx\\Debug\\334c1u2k\\x86\\android_gradle_build.json due to:", "file_": "C:\\Users\\<USER>\\Desktop\\PE\\MMA301PE_250801_SE172625\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "C:\\Users\\<USER>\\Desktop\\PE\\MMA301PE_250801_SE172625\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Program Files\\\\Android\\\\Android Studio\\\\jbr\\\\bin\\\\java\" ^\n  --class-path ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\modules-2\\\\files-2.1\\\\com.google.prefab\\\\cli\\\\2.1.0\\\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\\\cli-2.1.0-all.jar\" ^\n  com.google.prefab.cli.AppKt ^\n  --build-system ^\n  cmake ^\n  --platform ^\n  android ^\n  --abi ^\n  x86 ^\n  --os-version ^\n  24 ^\n  --stl ^\n  c++_shared ^\n  --ndk-version ^\n  27 ^\n  --output ^\n  \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\agp-prefab-staging10362896324256199677\\\\staged-cli-output\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.13\\\\transforms\\\\ecf58a1d5a3b5cc4faabc13e873181f2\\\\transformed\\\\react-android-0.79.5-debug\\\\prefab\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.13\\\\transforms\\\\35675df85bd02bb3d2682c8049acc4ba\\\\transformed\\\\hermes-android-0.79.5-debug\\\\prefab\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.13\\\\transforms\\\\492d12ddde8d0414cdeaa8d1c9a89b37\\\\transformed\\\\fbjni-0.7.0\\\\prefab\"\n", "file_": "C:\\Users\\<USER>\\Desktop\\PE\\MMA301PE_250801_SE172625\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "C:\\Users\\<USER>\\Desktop\\PE\\MMA301PE_250801_SE172625\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from 'C:\\Users\\<USER>\\Desktop\\PE\\MMA301PE_250801_SE172625\\android\\app\\.cxx\\Debug\\334c1u2k\\x86'", "file_": "C:\\Users\\<USER>\\Desktop\\PE\\MMA301PE_250801_SE172625\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder 'C:\\Users\\<USER>\\Desktop\\PE\\MMA301PE_250801_SE172625\\android\\app\\.cxx\\Debug\\334c1u2k\\x86'", "file_": "C:\\Users\\<USER>\\Desktop\\PE\\MMA301PE_250801_SE172625\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HC:\\\\Users\\\\<USER>\\\\Desktop\\\\PE\\\\MMA301PE_250801_SE172625\\\\node_modules\\\\react-native\\\\ReactAndroid\\\\cmake-utils\\\\default-app-setup\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=x86\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\\\Users\\\\<USER>\\\\Desktop\\\\PE\\\\MMA301PE_250801_SE172625\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\334c1u2k\\\\obj\\\\x86\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\\\Users\\\\<USER>\\\\Desktop\\\\PE\\\\MMA301PE_250801_SE172625\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\334c1u2k\\\\obj\\\\x86\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=C:\\\\Users\\\\<USER>\\\\Desktop\\\\PE\\\\MMA301PE_250801_SE172625\\\\android\\\\app\\\\.cxx\\\\Debug\\\\334c1u2k\\\\prefab\\\\x86\\\\prefab\" ^\n  \"-BC:\\\\Users\\\\<USER>\\\\Desktop\\\\PE\\\\MMA301PE_250801_SE172625\\\\android\\\\app\\\\.cxx\\\\Debug\\\\334c1u2k\\\\x86\" ^\n  -GNinja ^\n  \"-DPROJECT_BUILD_DIR=C:\\\\Users\\\\<USER>\\\\Desktop\\\\PE\\\\MMA301PE_250801_SE172625\\\\android\\\\app\\\\build\" ^\n  \"-DPROJECT_ROOT_DIR=C:\\\\Users\\\\<USER>\\\\Desktop\\\\PE\\\\MMA301PE_250801_SE172625\\\\android\" ^\n  \"-DREACT_ANDROID_DIR=C:\\\\Users\\\\<USER>\\\\Desktop\\\\PE\\\\MMA301PE_250801_SE172625\\\\node_modules\\\\react-native\\\\ReactAndroid\" ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\"\n", "file_": "C:\\Users\\<USER>\\Desktop\\PE\\MMA301PE_250801_SE172625\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HC:\\\\Users\\\\<USER>\\\\Desktop\\\\PE\\\\MMA301PE_250801_SE172625\\\\node_modules\\\\react-native\\\\ReactAndroid\\\\cmake-utils\\\\default-app-setup\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=x86\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\\\Users\\\\<USER>\\\\Desktop\\\\PE\\\\MMA301PE_250801_SE172625\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\334c1u2k\\\\obj\\\\x86\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\\\Users\\\\<USER>\\\\Desktop\\\\PE\\\\MMA301PE_250801_SE172625\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\334c1u2k\\\\obj\\\\x86\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=C:\\\\Users\\\\<USER>\\\\Desktop\\\\PE\\\\MMA301PE_250801_SE172625\\\\android\\\\app\\\\.cxx\\\\Debug\\\\334c1u2k\\\\prefab\\\\x86\\\\prefab\" ^\n  \"-BC:\\\\Users\\\\<USER>\\\\Desktop\\\\PE\\\\MMA301PE_250801_SE172625\\\\android\\\\app\\\\.cxx\\\\Debug\\\\334c1u2k\\\\x86\" ^\n  -GNinja ^\n  \"-DPROJECT_BUILD_DIR=C:\\\\Users\\\\<USER>\\\\Desktop\\\\PE\\\\MMA301PE_250801_SE172625\\\\android\\\\app\\\\build\" ^\n  \"-DPROJECT_ROOT_DIR=C:\\\\Users\\\\<USER>\\\\Desktop\\\\PE\\\\MMA301PE_250801_SE172625\\\\android\" ^\n  \"-DREACT_ANDROID_DIR=C:\\\\Users\\\\<USER>\\\\Desktop\\\\PE\\\\MMA301PE_250801_SE172625\\\\node_modules\\\\react-native\\\\ReactAndroid\" ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\"\n", "file_": "C:\\Users\\<USER>\\Desktop\\PE\\MMA301PE_250801_SE172625\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "C:\\Users\\<USER>\\Desktop\\PE\\MMA301PE_250801_SE172625\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of C:\\Users\\<USER>\\Desktop\\PE\\MMA301PE_250801_SE172625\\android\\app\\.cxx\\Debug\\334c1u2k\\x86\\compile_commands.json.bin normally", "file_": "C:\\Users\\<USER>\\Desktop\\PE\\MMA301PE_250801_SE172625\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "C:\\Users\\<USER>\\Desktop\\PE\\MMA301PE_250801_SE172625\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked C:\\Users\\<USER>\\Desktop\\PE\\MMA301PE_250801_SE172625\\android\\app\\.cxx\\Debug\\334c1u2k\\x86\\compile_commands.json to C:\\Users\\<USER>\\Desktop\\PE\\MMA301PE_250801_SE172625\\android\\app\\.cxx\\tools\\debug\\x86\\compile_commands.json", "file_": "C:\\Users\\<USER>\\Desktop\\PE\\MMA301PE_250801_SE172625\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "C:\\Users\\<USER>\\Desktop\\PE\\MMA301PE_250801_SE172625\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]